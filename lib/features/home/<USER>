import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

import '../../routing/app_router.dart';
import '../profile_menu/profile_menu.dart';
import 'contract/home_contract.dart';
import 'home_presenter.dart';
import 'widgets/language_list_item.dart';
import 'widgets/word_of_the_day_card.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> with RouteAware {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.unsubscribe(this);
    routeObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  void didPopNext() {
    super.didPopNext();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final presenter =
          ref.read(homePresenterProvider(key: widget.key!).notifier);
      presenter.intentHandler(const HomeIntent.returned());
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(homePresenterProvider(key: widget.key!));
    final presenter =
        ref.read(homePresenterProvider(key: widget.key!).notifier);
    _handleSideEffects(context);

    return AdMobScaffold(
      key: widget.key,
      appBar: AppBar(
        backgroundColor: Palette.surface,
        elevation: 0,
        titleSpacing: 16,
        title: SvgPicture.asset(
          'assets/images/ic_cussme.svg',
          height: 24,
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: Palette.primary),
            onPressed: () =>
                presenter.intentHandler(const HomeIntent.navigateToSearch()),
          ),
          ProfileMenu(key: widget.key, isGuest: state.isGuest),
          const SizedBox(width: 4),
        ],
      ),
      body: state.loadingState == ScreenLoadingState.loaded
          ? _buildHomeContent(context, state, presenter)
          : ScreenLoading(
              state: state.loadingState,
              onRetry: () => presenter.intentHandler(
                const HomeIntent.retry(),
              ),
            ),
    );
  }

  Widget _buildHomeContent(
      BuildContext context, HomeState state, HomePresenter presenter) {
    return Column(
      children: [
        if (state.words.isNotEmpty) ...[
          _buildWordOfTheDaySection(context, state, presenter),
          const SizedBox(height: 24),
        ],
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Divider(height: 1, color: Palette.outlineVariant),
                _buildLanguageList(state, presenter),
                const Divider(height: 1, color: Palette.outlineVariant),
                _buildAddLanguageButton(context, presenter)
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildWordOfTheDaySection(
      BuildContext context, HomeState state, HomePresenter presenter) {
    return SizedBox(
      height: 200,
      child: AnimatedPageView(
        itemCount: state.words.length,
        controller: state.pageController?.controller,
        clipBehavior: Clip.none,
        padEnds: false,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        onPageChanged: (index) {
          presenter.intentHandler(HomeIntent.changeWordIndex(index));
        },
        itemBuilder: (context, index, pageOffset, transitionProgress) {
          print('transitionProgress: $transitionProgress');
          final word = state.words[index];
          return WordOfTheDayCard(
            word: word,
            isActive: index == state.currentWordIndex,
            transitionProgress: transitionProgress,
            onTap: () => presenter.intentHandler(index != state.currentWordIndex
                ? HomeIntent.changeWordIndex(index)
                : HomeIntent.navigateToWordDetail(word.id)),
            onPlayPronunciation: () =>
                presenter.intentHandler(PlayPronunciationIntent(word)),
          );
        },
      ),
    );
  }

  Widget _buildLanguageList(HomeState state, HomePresenter presenter) =>
      ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: state.languages.length,
        separatorBuilder: (context, index) =>
            const Divider(height: 1, color: Palette.outlineVariant),
        itemBuilder: (context, index) {
          final language = state.languages[index];
          return LanguageListItem(
            language: language,
            onTap: () => presenter
                .intentHandler(HomeIntent.navigateToWordList(language)),
          );
        },
      );

  Widget _buildAddLanguageButton(
          BuildContext context, HomePresenter presenter) =>
      Padding(
        padding: const EdgeInsets.all(16.0),
        child: ElevatedButton.icon(
          onPressed: () =>
              presenter.intentHandler(const HomeIntent.navigateToLanguages()),
          style: ElevatedButton.styleFrom(
            backgroundColor: Palette.surface,
            foregroundColor: Palette.primary,
            elevation: 0,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          ),
          icon: const Icon(Icons.add, size: 20),
          label: Text(
            Str.of(context).addMoreLanguages,
            style: TextStyles.labelLarge.copyWith(
              color: Palette.primary,
            ),
          ),
        ),
      );

  void _handleSideEffects(BuildContext context) {
    ref.listen(homeSideEffectsProvider(key: widget.key!), (_, next) {
      next.whenData((sideEffect) {
        switch (sideEffect) {
          case ShowErrorSideEffect _:
            Toast.show(context, sideEffect.message);
            break;
          case NavigateToLanguagesSideEffect _:
            GoRouter.of(context)
                .pushToLanguages(source: Str.of(context).dashboard);
            break;
          case final NavigateToWordDetailSideEffect se:
            GoRouter.of(context).pushToWordDetail(wordId: se.wordId);
            break;
          case final NavigateToWordListSideEffect se:
            GoRouter.of(context).pushToWordList(
              language: se.language,
              source: Str.of(context).dashboard,
            );
            break;
          case NavigateToSignInSideEffect _:
            GoRouter.of(context).pushToSignIn();
            break;
          case NavigateToSearchSideEffect _:
            GoRouter.of(context).pushToSearch();
            break;
        }
      });
    });
  }
}
