import 'package:flutter/material.dart';

/// A custom PageView that applies smooth transform animations to its children
/// based on their position relative to the current page
class AnimatedPageView extends StatefulWidget {
  final int itemCount;
  final PageController? controller;
  final ValueChanged<int>? onPageChanged;
  final Widget Function(BuildContext context, int index, double pageOffset,
      double transitionProgress, int currentPageIndex) itemBuilder;
  final Clip clipBehavior;
  final bool padEnds;
  final EdgeInsetsGeometry? padding;

  const AnimatedPageView({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.controller,
    this.onPageChanged,
    this.clipBehavior = Clip.hardEdge,
    this.padEnds = true,
    this.padding,
  });

  @override
  State<AnimatedPageView> createState() => _AnimatedPageViewState();
}

class _AnimatedPageViewState extends State<AnimatedPageView> {
  late PageController _pageController;
  double _currentPage = 0.0;

  @override
  void initState() {
    super.initState();
    _pageController =
        widget.controller ?? PageController(viewportFraction: 0.8);
    _currentPage = _pageController.initialPage.toDouble();

    _pageController.addListener(_onPageChanged);
  }

  @override
  void dispose() {
    _pageController.removeListener(_onPageChanged);
    if (widget.controller == null) {
      _pageController.dispose();
    }
    super.dispose();
  }

  void _onPageChanged() {
    if (_pageController.hasClients) {
      setState(() {
        _currentPage = _pageController.page ?? 0.0;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding,
      child: PageView.builder(
        controller: _pageController,
        itemCount: widget.itemCount,
        clipBehavior: widget.clipBehavior,
        padEnds: widget.padEnds,
        onPageChanged: widget.onPageChanged,
        itemBuilder: (context, index) {
          // Calculate the offset of this page from the current page
          final pageOffset = index - _currentPage;

          return _buildTransformedItem(context, index, pageOffset);
        },
      ),
    );
  }

  Widget _buildTransformedItem(
      BuildContext context, int index, double pageOffset) {
    // Calculate scale based on distance from center
    // Active page (offset = 0) has scale 1.0
    // Pages further away have smaller scale
    final scale = _calculateScale(pageOffset);

    // Calculate opacity for smooth fade effect
    final opacity = _calculateOpacity(pageOffset);

    // Calculate vertical translation for subtle depth effect
    final verticalOffset = _calculateVerticalOffset(pageOffset);

    // Calculate transition progress for color/style interpolation
    // 1.0 = fully active, 0.0 = fully inactive
    final transitionProgress = _calculateTransitionProgress(pageOffset);

    // Use calculated values for smooth transitions
    final currentPageIndex = _currentPage.round();
    final finalScale = scale;
    final finalOpacity = opacity;

    return Transform.translate(
      offset: Offset(0, verticalOffset),
      child: Transform.scale(
        scale: finalScale,
        child: Opacity(
          opacity: finalOpacity,
          child: widget.itemBuilder(
              context, index, pageOffset, transitionProgress, currentPageIndex),
        ),
      ),
    );
  }

  double _calculateScale(double pageOffset) {
    // Scale from 1.0 (active) to 0.85 (inactive)
    const minScale = 0.85;
    const maxScale = 1.0;

    // Use absolute value to handle both left and right pages
    final absOffset = pageOffset.abs();

    if (absOffset <= 1.0) {
      // Use a smooth curve for more natural animation
      final normalizedOffset = absOffset.clamp(0.0, 1.0);
      return maxScale - (normalizedOffset * (maxScale - minScale));
    } else {
      return minScale;
    }
  }

  double _calculateOpacity(double pageOffset) {
    // Keep full opacity for pages close to center
    const minOpacity = 0.8;
    const maxOpacity = 1.0;

    final absOffset = pageOffset.abs();

    if (absOffset <= 1.0) {
      // Use a gentler curve for opacity changes
      final normalizedOffset = absOffset.clamp(0.0, 1.0);
      return maxOpacity - (normalizedOffset * (maxOpacity - minOpacity));
    } else {
      return minOpacity;
    }
  }

  double _calculateVerticalOffset(double pageOffset) {
    // Add subtle vertical movement for depth effect
    const maxVerticalOffset = 8.0;

    final absOffset = pageOffset.abs();

    if (absOffset <= 1.0) {
      // Inactive cards move slightly down
      final normalizedOffset = absOffset.clamp(0.0, 1.0);
      return normalizedOffset * maxVerticalOffset;
    } else {
      return maxVerticalOffset;
    }
  }

  double _calculateTransitionProgress(double pageOffset) {
    // Calculate transition progress for smooth color/style interpolation
    // Simple linear interpolation: closer to center = higher progress

    final absOffset = pageOffset.abs();

    if (absOffset >= 1.0) {
      // More than one page away -> fully inactive
      return 0.0;
    }

    // Simple linear interpolation
    // absOffset = 0.0 (center) -> progress = 1.0 (fully active)
    // absOffset = 1.0 (edge) -> progress = 0.0 (fully inactive)
    return (1.0 - absOffset).clamp(0.0, 1.0);
  }
}
